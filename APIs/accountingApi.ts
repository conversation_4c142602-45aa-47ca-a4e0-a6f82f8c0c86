// Accounting API functions for admin panel
import axios from 'axios';
import { toast } from 'react-toastify';
import { apiEndPoints } from '../utils/apiEndPoints';

// Get all accounts
export async function getAccountsRest(): Promise<any> {
  try {
    const res = await axios.get('/admin/accounting/accounts');
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message || 'Failed to fetch accounts');
    return { error: error?.response?.data || error };
  }
}

// Get single account by ID
export async function getAccountByIdRest(accountId: string): Promise<any> {
  try {
    const res = await axios.get(`/admin/accounting/account/${accountId}`);
    return res.data;
  } catch (error: any) {
    toast.error(error?.response?.data?.message || 'Failed to fetch account');
    return { error: error?.response?.data || error };
  }
}
