import React from 'react';
import { useRouter } from 'next/router';
import AccountDetail from '../../../components/accounting/accounts/AccountDetail';

const AccountDetailPage: React.FC = () => {
  const router = useRouter();
  const { accountId } = router.query;

  if (!accountId || typeof accountId !== 'string') return <div>Invalid account ID</div>;

  return <AccountDetail accountId={accountId} />;
};

export default AccountDetailPage;
