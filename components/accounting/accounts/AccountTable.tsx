import React from 'react';
import { useRouter } from 'next/router';

interface AccountTableProps {
  accounts: any[];
  loading: boolean;
}

const AccountTable: React.FC<AccountTableProps> = ({ accounts, loading }) => {
  const router = useRouter();

  if (loading) {
    return (
      <div className="text-center py-4">
        <div className="spinner-border" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="table-responsive">
      <table className="table table-striped">
        <thead>
          <tr>
            <th>ID</th>
            <th>Transaction Type</th>
            <th>Reference</th>
            <th>Reference ID</th>
            <th>Reference Type</th>
            <th>Amount</th>
            <th className="text-center">Actions</th>
          </tr>
        </thead>
        <tbody>
          {accounts.length === 0 ? (
            <tr>
              <td colSpan={7} className="text-center py-4">
                No accounts found
              </td>
            </tr>
          ) : (
            accounts.map((acc) => (
              <tr key={acc.id}>
                <td>{acc.id}</td>
                <td>{acc.transactionType}</td>
                <td>{acc.reference}</td>
                <td>{acc.referenceId}</td>
                <td>{acc.referenceType}</td>
                <td>{acc.amount}</td>
                <td className="text-center align-middle">
                  <button
                    className="btn btn-default btn-outline-info"
                    onClick={() => router.push(`/accounting/accounts/${acc.id}`)}
                  >
                    <span>
                      <i className="bi bi-eye me-2 align-middle"></i>
                    </span>
                    View
                  </button>
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
};

export default AccountTable;
