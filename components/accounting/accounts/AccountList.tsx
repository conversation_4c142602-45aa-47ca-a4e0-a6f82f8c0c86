import React, { useEffect, useLayoutEffect, useState } from 'react';
import { userAPI } from '../../../APIs';
import TaskPagination from '../../tasks/TaskPagination';
import AccountTable from './AccountTable';

const PAGE_LIMIT = 10;

const AccountList: React.FC = () => {
  const [accounts, setAccounts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [skip, setSkip] = useState(0);
  const [total, setTotal] = useState(0);

  useLayoutEffect(() => {
    setLoading(false);
  }, []);

  useLayoutEffect(() => {
    setLoading(false);
  }, []);

  useEffect(() => {
    const fetchAccounts = async () => {
      setLoading(true);
      setError(null);
      const res = await userAPI.getAccounts();
      let data = Array.isArray(res) ? res : res?.data || [];
      setTotal(data.length);
      setAccounts(data.slice(skip, skip + PAGE_LIMIT));
      if (res?.error) {
        setError(res.error.message || 'Failed to fetch accounts');
      }
      setLoading(false);
    };
    fetchAccounts();
  }, [skip]);

  return (
    <div className="card mt-4">
      <div className="card-body">
        <div className="d-flex justify-content-between align-items-center mb-3">
          <h4 className="mb-0">Accounts</h4>
        </div>
        <AccountTable accounts={accounts} loading={loading} />
        <TaskPagination
          pagination={{ skip, limit: PAGE_LIMIT, total }}
          onPageChange={setSkip}
        />
        {error && <div className="text-danger mt-2">{error}</div>}
      </div>
    </div>
  );
};

export default AccountList;
