import React, { useEffect, useState } from 'react';
import { userAPI } from '../../../APIs';

interface AccountDetailProps {
  accountId: string;
}

const AccountDetail: React.FC<AccountDetailProps> = ({ accountId }) => {
  const [account, setAccount] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAccount = async () => {
      setLoading(true);
      setError(null);
      const res = await userAPI.getAccountById(accountId);
      if (res?.error) {
        setError(res.error.message || 'Failed to fetch account');
      } else {
        setAccount(res.data || res);
      }
      setLoading(false);
    };
    fetchAccount();
  }, [accountId]);

  if (loading) return <div>Loading account...</div>;
  if (error) return <div style={{ color: 'red' }}>{error}</div>;
  if (!account) return <div>No account found.</div>;

  return (
    <div>
      <h2>Account Detail</h2>
      <ul>
        <li><b>ID:</b> {account.id}</li>
        <li><b>Transaction Type:</b> {account.transactionType}</li>
        <li><b>Reference:</b> {account.reference}</li>
        <li><b>Reference ID:</b> {account.referenceId}</li>
        <li><b>Reference Type:</b> {account.referenceType}</li>
        <li><b>Amount:</b> {account.amount}</li>
      </ul>
      <a href="/accounting/accounts">Back to Accounts</a>
    </div>
  );
};

export default AccountDetail;
